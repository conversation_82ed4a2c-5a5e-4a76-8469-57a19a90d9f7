#!/usr/bin/env python3

import os
import re

def find_doc_patterns():
    """Find files with potential doc directive patterns that might confuse dart_eval"""
    
    # Patterns that might be interpreted as doc directives
    patterns = [
        r'\{@\w+',  # Any {@word pattern
        r'`[^`]*\{@[^}]*`',  # Backticks containing {@
        r'///.*\{@',  # Doc comments with {@
        r'/\*.*\{@.*\*/',  # Block comments with {@
        r"'[^']*\{@[^']*'",  # Single quotes with {@
        r'"[^"]*\{@[^"]*"',  # Double quotes with {@
    ]
    
    issues = []
    
    for root, dirs, files in os.walk('.'):
        # Skip certain directories
        if any(skip in root for skip in ['.dart_tool', '.git', 'build', 'node_modules']):
            continue
            
        for file in files:
            if file.endswith('.dart'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    for i, pattern in enumerate(patterns):
                        matches = re.finditer(pattern, content, re.DOTALL)
                        for match in matches:
                            issues.append({
                                'file': file_path,
                                'pattern': pattern,
                                'match': match.group(),
                                'start': match.start(),
                                'end': match.end(),
                                'line': content[:match.start()].count('\n') + 1
                            })
                            
                except Exception as e:
                    print(f"Error reading {file_path}: {e}")
    
    return issues

if __name__ == "__main__":
    issues = find_doc_patterns()
    
    if issues:
        print("Found potential doc directive patterns:")
        for issue in issues[:20]:  # Show first 20 issues
            print(f"File: {issue['file']}")
            print(f"Line: {issue['line']}")
            print(f"Pattern: {issue['pattern']}")
            print(f"Match: {repr(issue['match'])}")
            print(f"Position: {issue['start']}-{issue['end']}")
            print("-" * 50)
    else:
        print("No doc directive patterns found.")
